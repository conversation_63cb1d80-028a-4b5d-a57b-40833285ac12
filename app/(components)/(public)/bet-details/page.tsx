'use client';

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Image from 'next/image';

// Types for the API response
interface BetDetailsData {
  provider: string;
  marketDetail: {
    marketId: string;
    marketName: string;
    marketStatus: string;
  };
  betDetails: {
    betId: string;
    settlementStatus: string;
    betAmount: number;
    settlementAmount: number;
    createdDate: string;
    payoutStatus: number;
    status: string;
    betQrCode: string;
  };
  betList: Array<{
    betId: string;
    marketName: string;
    rate: number;
    stake: number;
  }>;
  customerSupportDetails: {
    id: string;
    phone: string;
    email: string;
  };
}

interface ApiResponse {
  code: number;
  message: string;
  success: number;
  data: BetDetailsData;
}

/**
 * Internal component that uses useSearchParams
 */
const BetDetailsContent: React.FC = () => {
  const searchParams = useSearchParams();
  const betId = searchParams.get('bet_id');

  const [betData, setBetData] = useState<ApiResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API fetch function with all required headers
  const fetchBetDetails = async (transactionId: string): Promise<ApiResponse> => {
    const myHeaders = new Headers();
    myHeaders.append("accept", "application/json, text/plain, */*");
    myHeaders.append("accept-language", "en-GB,en-US;q=0.9,en;q=0.8");
    myHeaders.append("access-control-allow-origin", "*");
    myHeaders.append("cache-control", "no-cache");
    myHeaders.append("content-type", "application/json");
    myHeaders.append("domain", "https://www.hal567.com");
    myHeaders.append("expires", "0");
    myHeaders.append("language", "EN-US");
    myHeaders.append("origin", "https://www.hal567.com");
    myHeaders.append("pragma", "no-cache");
    myHeaders.append("priority", "u=1, i");
    myHeaders.append("referer", "https://www.hal567.com/bet-details/685926cc6f508ab8d275e03d/turbostars?trId=40bd7c9e-c359-464c-baea-6f5459c7142e");
    myHeaders.append("sec-ch-ua", "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"");
    myHeaders.append("sec-ch-ua-mobile", "?0");
    myHeaders.append("sec-ch-ua-platform", "\"Linux\"");
    myHeaders.append("sec-fetch-dest", "empty");
    myHeaders.append("sec-fetch-mode", "cors");
    myHeaders.append("sec-fetch-site", "same-site");
    myHeaders.append("signature", "U2FsdGVkX19YMFPzadETKI3eAFp2QhdO1z7EYNSh3UzK3WD/MtzR74ZnHeribyFA9j6S90InmJjNbjbQPYcJM0HgeNljKjAGJE/8Dw/0b534xaypuyJwtZgAfciS8wEUEGsZcSEG6GvnFcf3cE4MQ4fTcB7lpEyMsPy7AmXcElE=");
    myHeaders.append("user-agent", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36");

    const response = await fetch("https://api.ingrandstation.com/turboStars/betResults", {
      method: "POST",
      headers: myHeaders,
      body: JSON.stringify({
        "providerName": "turbostars",
        "transactionId": transactionId
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result: ApiResponse = await response.json();

    if (result.success !== 1) {
      throw new Error(result.message || 'Failed to fetch bet details');
    }

    return result;
  };

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount / 100); // Assuming amount is in cents
  };

  // Format date
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      });
    } catch (error) {
      //eslint-disable-next-line no-console
      console.error(error, 'error');
      return dateString;
    }
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'win':
        return 'text-green-400';
      case 'loss':
      case 'lose':
        return 'text-red-400';
      case 'pending':
        return 'text-yellow-400';
      case 'void':
        return 'text-gray-400';
      default:
        return 'text-white';
    }
  };

  // Get payout status text
  const getPayoutStatusText = (payoutStatus: number): string => {
    switch (payoutStatus) {
      case 1:
        return 'Settled';
      case 2:
        return 'Unsettled';
      case 3:
        return 'Settled';
      default:
        return 'Unknown';
    }
  };

  useEffect(() => {
    const loadBetDetails = async () => {
      if (!betId) {
        setError('Bet ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const data = await fetchBetDetails(betId);
        setBetData(data);
      } catch (err) {
        //eslint-disable-next-line no-console
        console.error('Error loading bet details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load bet details');
      } finally {
        setIsLoading(false);
      }
    };

    loadBetDetails();
  }, [betId]); // This will trigger when betId changes

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-xl mb-4">Error Loading Bet Details</div>
          <div className="text-white text-lg mb-6">{error}</div>
          {!betId && (
            <div className="text-gray-400 text-sm">
              Please provide a valid bet_id parameter in the URL
            </div>
          )}
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-golden mx-auto mb-4"></div>
          <div className="text-white text-xl">Loading bet details...</div>
        </div>
      </div>
    );
  }

  if (!betData) {
    return (
      <div className="min-h-screen bg-bodybg flex items-center justify-center">
        <div className="text-white text-xl">No bet details available</div>
      </div>
    );
  }

  const { data } = betData;

  // Data-driven configs for sections
  const marketDetailsFields = [
    { label: 'Market ID', value: data.marketDetail.marketId },
    { label: 'Market Name', value: data.marketDetail.marketName, className: 'text-right max-w-[60%]' },
    { label: 'Market Status', value: data.marketDetail.marketStatus },
  ];

  const betDetailsFields = [
    { label: 'Bet ID', value: data.betDetails.betId },
    { label: 'Settlement Status', value: data.betDetails.settlementStatus },
    { label: 'Bet Amount', value: formatCurrency(data.betDetails.betAmount) },
    { label: 'Settlement Amount', value: formatCurrency(data.betDetails.settlementAmount) },
    { label: 'Created Date', value: formatDate(data.betDetails.createdDate) },
    { label: 'Status', value: data.betDetails.status, className: getStatusColor(data.betDetails.status) },
    { label: 'Payout Status', value: getPayoutStatusText(data.betDetails.payoutStatus) },
  ];

  const customerSupportFields = [
    { label: 'Support ID', value: data.customerSupportDetails.id },
    { label: 'Phone', value: data.customerSupportDetails.phone },
    { label: 'Email', value: data.customerSupportDetails.email },
  ];

  return (
    <div className="min-h-screen bg-[#0F0F0F] text-white">
      {/* Logo Section */}
      <div className=" max-w-4xl mx-auto flex justify-between py-6 gap-2">
        <div className="relative rounded-lg  overflow-hidden  h-[80px] bg-purple-header-gradient-1">
          <div className="absolute left-[0px] top-1/2 transform -translate-y-1/2">
            <Image
              src="/bet-slip-icon-print.png"
              alt="Bet slip print icon"
              className="w-[70px] h-[70px]"
              width={70}
              height={70}
            />
          </div>
          <div className="relative z-10 flex items-center justify-between h-full px-6">
            <div className="flex-1 pl-10">
              <h1 className="text-white text-2xl font-medium">Betslip</h1>
            </div>
          </div>
        </div>
        <div className="w-[100px] h-[100px] relative">
          <Image
            src="/assets/images/Golden-Island.webp"
            alt="Golden Island Logo"
            fill
            className="object-contain"
            priority
          />
        </div>
      </div>

      {/* Bet Details Content */}
      <div className="max-w-4xl mx-auto mb-5 p-6 pb-8 bg-[#1D1D1F] rounded-lg">
        {/* Market Details Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Market Details
          </h2>
          <div className="space-y-3">
            {marketDetailsFields.map((field) => (
              <div key={field.label} className="flex justify-between items-center">
                <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{field.label}:</span>
                <span className={`font-rubik font-medium text-lg leading-none capitalize text-white ${field.className || ''}`}>{field.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Bet Details Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Bet Details
          </h2>
          <div className="space-y-3">
            {betDetailsFields.map((field) => (
              <div key={field.label} className="flex justify-between items-center">
                <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{field.label}:</span>
                <span className={`font-rubik font-medium text-lg leading-none capitalize ${field.className || 'text-white'}`}>{field.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Individual Bets Section */}
        {data.betList && data.betList.length > 0 && (
          <div className="mb-8">
            <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
              Individual Bets
            </h2>
            <div className="space-y-6">
              {data.betList.map((bet, index) => (
                <div key={index} className="bg-surface p-4 rounded-lg">
                  <h3 className="font-rubik font-medium text-lg leading-none pb-2 capitalize text-white mb-3 border-b border-[#1D1D1F]">Bet {index + 1}</h3>
                  <div className="space-y-3">
                    {[
                      { label: 'Bet ID', value: bet.betId },
                      { label: 'Market Name', value: bet.marketName, className: 'text-right max-w-[60%]' },
                      { label: 'Rate', value: bet.rate },
                      { label: 'Stake', value: formatCurrency(bet.stake) },
                    ].map((field) => (
                      <div key={field.label} className="flex justify-between items-center">
                        <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{field.label}:</span>
                        <span className={`font-rubik font-medium text-lg leading-none capitalize text-white ${field.className || ''}`}>{field.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Customer Support Section */}
        <div className="mb-8">
          <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
            Customer Support
          </h2>
          <div className="space-y-3">
            {customerSupportFields.map((field) => (
              <div key={field.label} className="flex justify-between items-center">
                <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{field.label}:</span>
                <span className="font-rubik font-medium text-lg leading-none capitalize text-white">{field.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* QR Code Section */}
        {/* {data.betDetails.betQrCode && (
          <div className="text-center">
            <h2 className="font-rubik font-bold text-2xl leading-none capitalize text-filter-placeholder border-b border-[#3A3A3A] pb-2 mb-4">
              QR Code
            </h2>
            <div className="inline-block p-4 bg-white rounded-lg">
              <div className="w-32 h-32 bg-gray-200 flex items-center justify-center text-black text-sm">
                QR Code
              </div>
            </div>
            <p className="font-rubik font-medium text-lg leading-none capitalize text-white mt-2">
              Scan for bet verification
            </p>
          </div>
        )} */}
      </div>
    </div>
  );
};

/**
 * Public Bet Details Page with Suspense boundary
 *
 * A public page that displays bet details without requiring authentication.
 * Accessible via URL: /bet-details?bet_id=<transaction_id>
 */
const BetDetailsPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <div className="text-white text-xl">Loading...</div>
        </div>
      </div>
    }>
      <BetDetailsContent />
    </Suspense>
  );
};

export default BetDetailsPage;
